<?php
/**
 * Provides a place to register WooCommerce related hooks and filters
 */

namespace App\Includes;

use Roots\Acorn\Sage\SageServiceProvider;

class WooCommerceHooks extends SageServiceProvider
{

    public function __construct()
    {
        parent::__construct();
    }

   public function register()
   {
        parent::register();

        add_action('template_redirect', array($this, 'template_redirect_hook'));
        add_filter('wpseo_breadcrumb_links', array($this, 'add_shop_url_to_breadcrumbs'));
        add_filter('woocommerce_login_redirect', array($this, 'redirect_to_shop_after_login'));
        add_filter('woocommerce_style_smallscreen_breakpoint',array($this, 'woo_custom_breakpoint'));
        add_filter('woocommerce_product_upsells_products_heading', array($this, 'change_product_upsell_title') );
        add_filter('woocommerce_upsell_display_args', array($this, 'change_number_of_related_products'), 20 );
        add_filter('woocommerce_available_payment_gateways',array($this, 'filter_gateways'),1);
   } 

   public function template_redirect_hook()
   {
        $this->redirect_to_child_product_page();
        $this->redirect_to_shop_if_on_category_page();
        $this->redirect_dontlinkthrough_posts();
   }

   /**
    * Redirect to child product
    */
    public function redirect_to_child_product_page()
    {
        $category = get_queried_object();
        if (is_archive() && isset($category->count) && $category->count === 1) {
            $child = get_terms($category->taxonomy, array(
                'parent' => $category->term_id,
                'hide_empty' => false
            ));
            wp_safe_redirect(get_permalink($child));
            exit;
        }
    }

    /**
     * Redirect to shop page if on product category archive page
     */
    public function redirect_to_shop_if_on_category_page()
    {
        // Only on product category archive pages (redirect to shop)
        if (is_product_category()) {
            wp_redirect(wc_get_page_permalink('shop'));
            exit();
        }
    }

    /**
     * Redirect to post archive if post has 'dont_link_through' field set to true
     */
    public function redirect_dontlinkthrough_posts()
    {
        global $post;
        if (is_single() && get_field('dont_link_through', $post->ID)) {
            $post_type_archive_url = get_post_type_archive_link('post');
            wp_redirect($post_type_archive_url, 301);
            exit;
        }
    }

    /**
     * Add Shop URL to breadcrumbs
     */
    public function add_shop_url_to_breadcrumbs($links)
    {
        global $post;

        if (is_woocommerce() && !is_shop()) {
            $breadcrumb[] = array(
                'url' => get_permalink(woocommerce_get_page_id('shop')),
                'text' => 'Shop',
            );

            array_splice($links, 1, -2, $breadcrumb);
        }

        return $links;
    }

    /**
     * After login, redirect to shop page if not on checkout page
     * 
     * @param string $redirect
     * @return string
     */
    public function redirect_to_shop_after_login( $redirect) {
        $redirect_page_id = url_to_postid( $redirect );
        $checkout_page_id = wc_get_page_id( 'checkout' );

        if( $redirect_page_id == $checkout_page_id ) {
            return $redirect;
        }

        return wc_get_page_permalink( 'shop' );
    }

    /**
    * Change Woocommerce css breaktpoint max width
    * 
    * @param string $px
    * @return string
    */
    public function woo_custom_breakpoint($px) {
        $px = '1200px';
        return $px;
    }

    /**
     * Change product upsell title
     */
    public function change_product_upsell_title() {
        return __( 'Related Products');
    }

    /**
     * Change number of upsells output
     * 
     * @param array $args
     * @return array
     */
    public function change_number_of_related_products( $args ) {
        $args['posts_per_page'] = 3;
        $args['columns'] = 3; //change number of upsells here
        return $args;
    }

    function hide_pay_over_phone($gateways){

        // Get Current user role
        dd($this->wordpress_hooks->get_user_role());
        
        if( is_user_logged_in()  ){
        unset($gateways['cod']);
        }
        
        return $gateways;
    }

}
