<?php
/**
 * Provides a place to register WooCommerce related hooks and filters
 */

namespace App\Includes;

use Roots\Acorn\Sage\SageServiceProvider;

class WordPressHooks extends SageServiceProvider
{
    public function register()
    {
        parent::register();
        
        add_filter( 'excerpt_more', '__return_empty_string', 21 );
        add_filter('body_class',array($this, 'add_user_role_to_body_class'));

        add_filter('excerpt_length', function() { return 15; } );
    }

    /**
     * Get Current User Role
     *
     * @return string|array
     */
    protected function get_user_role() {
        global $current_user;
        $user_roles = $current_user->roles;
        $user_roles_list = implode(" ", $user_roles);
        return $user_roles_list;
    }

    /**
     * Add Current User Role to Body Class
     * @param string $classes
     * @return array
     */
    public function add_user_role_to_body_class($classes) {
        $classes[] = $this->get_user_role();
        return $classes;
    }
}   